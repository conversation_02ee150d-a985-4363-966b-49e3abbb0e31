{"extends": "../../tsconfig.base.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "paths": {"@/*": ["./*"], "@lib/*": ["../../packages/api/lib/*"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}