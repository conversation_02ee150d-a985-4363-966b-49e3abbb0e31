'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  ArrowLeft,
  Users,
  Activity,
  Clock,
  TrendingUp,
  Edit,
  Save,
  X,
  Plus,
  Eye,
  BarChart3,
  Globe,
  MessageCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  approved: boolean;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
}

interface Club {
  id: string;
  name: string;
  description: string;
  contactEmail: string;
  contactPhone: string;
  clubRoom: string;
  createdAt: string;
  updatedAt: string;
  leader: {
    id: string;
    name: string;
    email: string;
  };
  memberCount?: number;
  eventCount?: number;
  totalHours?: number;
  status?: 'active' | 'inactive';
  category?: string;
  website?: string;
}

interface Member {
  id: string;
  name: string;
  email: string;
  role: string;
  joinedAt: string;
  hoursContributed: number;
  eventsAttended: number;
}

export default function ClubDetailsPage({ params }: { params: { id: string } }) {
  const { token } = useAuth();
  const router = useRouter();
  const [club, setClub] = useState<Club | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedClub, setEditedClub] = useState<Partial<Club>>({});
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'members' | 'analytics'>('overview');

  useEffect(() => {
    if (token) {
      fetchClubDetails();
      fetchClubEvents();
      fetchClubMembers();
    }
  }, [token, params.id]);

  const fetchClubDetails = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/clubs/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('Failed to fetch club details');

      const data = await response.json();

      // Enhance club data with mock additional fields for demo
      const enhancedClub = {
        ...data,
        memberCount: data.memberCount || Math.floor(Math.random() * 50) + 10,
        eventCount: data.eventCount || Math.floor(Math.random() * 20) + 5,
        totalHours: data.totalHours || Math.floor(Math.random() * 500) + 100,
        status: data.status || 'active',
        category: data.category || 'Academic',
        website: data.website || `https://${data.name?.toLowerCase().replace(/\s+/g, '')}.club.com`
      };

      setClub(enhancedClub);
      setEditedClub(enhancedClub);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchClubEvents = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/events/club/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('Failed to fetch club events');

      const data = await response.json();
      setEvents(data);
    } catch (err) {
      console.error('Error fetching events:', err);
    }
  };

  const fetchClubMembers = async () => {
    try {
      // Mock members data for demo
      const mockMembers: Member[] = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'President',
          joinedAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(),
          hoursContributed: 120,
          eventsAttended: 15
        },
        {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'Vice President',
          joinedAt: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString(),
          hoursContributed: 95,
          eventsAttended: 12
        },
        {
          id: '3',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          role: 'Member',
          joinedAt: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000).toISOString(),
          hoursContributed: 45,
          eventsAttended: 8
        },
        {
          id: '4',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          role: 'Secretary',
          joinedAt: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),
          hoursContributed: 78,
          eventsAttended: 10
        }
      ];
      setMembers(mockMembers);
    } catch (err) {
      console.error('Error fetching members:', err);
    }
  };

  const handleUpdate = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/clubs/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(editedClub)
      });

      if (!response.ok) throw new Error('Failed to update club');
      
      const updatedClub = await response.json();
      setClub(updatedClub);
      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  if (loading) return <div className="text-gray-700">Loading club details...</div>;
  if (error) return <div className="text-red-500">{error}</div>;
  if (!club) return <div className="text-gray-700">Club not found</div>;

  return (
    <div className="max-w-7xl mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/administrator/clubs')}
            className="text-gray-700 border-gray-300 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clubs
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">{club?.name || 'Club Details'}</h1>
            <p className="text-gray-600">Manage club information and members</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => setIsEditing(!isEditing)}
            variant="outline"
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            {isEditing ? (
              <>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </>
            ) : (
              <>
                <Edit className="h-4 w-4 mr-2" />
                Edit Club
              </>
            )}
          </Button>
          {isEditing && (
            <Button
              onClick={handleUpdate}
              className="bg-[#2c3e50] hover:bg-[#34495e] text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          )}
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center text-red-700">
              <X className="h-5 w-5 mr-2" />
              <span>Error: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card className="bg-white border-gray-200">
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-700 mr-3"></div>
              <span className="text-gray-600">Loading club details...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Club Stats Cards */}
      {club && !loading && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-700">Members</p>
                  <p className="text-2xl font-bold text-blue-900">{club.memberCount || 0}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-700">Events</p>
                  <p className="text-2xl font-bold text-green-900">{club.eventCount || 0}</p>
                </div>
                <Calendar className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-700">Total Hours</p>
                  <p className="text-2xl font-bold text-purple-900">{club.totalHours || 0}</p>
                </div>
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-700">Status</p>
                  <p className="text-lg font-bold text-orange-900 capitalize">{club.status || 'Active'}</p>
                </div>
                <Activity className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Navigation Tabs */}
      {club && !loading && (
        <Card className="bg-white border-gray-200">
          <CardContent className="p-0">
            <div className="flex border-b border-gray-200">
              {[
                { id: 'overview', label: 'Overview', icon: Eye },
                { id: 'events', label: 'Events', icon: Calendar },
                { id: 'members', label: 'Members', icon: Users },
                { id: 'analytics', label: 'Analytics', icon: BarChart3 }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 bg-blue-50'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-white rounded-lg shadow-md p-8 mb-8">
        {isEditing ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Club Name</label>
              <Input
                value={editedClub.name || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedClub({ ...editedClub, name: e.target.value })}
                className="bg-white text-gray-800 border border-gray-300"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <Textarea
                value={editedClub.description || ''}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setEditedClub({ ...editedClub, description: e.target.value })}
                className="bg-white text-gray-800 border border-gray-300"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
              <Input
                type="email"
                value={editedClub.contactEmail || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedClub({ ...editedClub, contactEmail: e.target.value })}
                className="bg-white text-gray-800 border border-gray-300"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
              <Input
                type="tel"
                value={editedClub.contactPhone || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedClub({ ...editedClub, contactPhone: e.target.value })}
                className="bg-white text-gray-800 border border-gray-300"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Club Room</label>
              <Input
                value={editedClub.clubRoom || ''}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedClub({ ...editedClub, clubRoom: e.target.value })}
                className="bg-white text-gray-800 border border-gray-300"
              />
            </div>
            <Button
              onClick={handleUpdate}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Save Changes
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center gap-4 mb-2">
              <h2 className="text-2xl font-semibold text-gray-800">{club.name}</h2>
              <Badge variant="outline" className="border-gray-300 text-gray-700 bg-gray-50">
                {club.leader.name} (Leader)
              </Badge>
            </div>
            <p className="text-gray-700 text-base mb-4">{club.description}</p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="flex items-center gap-2 text-gray-600">
                <Mail className="h-4 w-4" />
                <span>{club.contactEmail}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <Phone className="h-4 w-4" />
                <span>{club.contactPhone}</span>
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{club.clubRoom}</span>
              </div>
            </div>
            <div className="text-sm text-gray-500 mb-2">
              <p>Leader Email: {club.leader.email}</p>
              <p>Created: {new Date(club.createdAt).toLocaleDateString()}</p>
              <p>Last Updated: {new Date(club.updatedAt).toLocaleDateString()}</p>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-gray-700" />
            <h3 className="text-lg font-semibold text-gray-800">Club Events</h3>
          </div>
        </div>
        <div className="space-y-4">
          {events && events.length > 0 ? (
            events.map((event) => (
              <div key={event.id} className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-gray-800">{event.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                  </div>
                  <Badge variant={event.approved ? "success" : "warning"} className={event.approved ? "bg-emerald-100 text-emerald-700 border-emerald-200" : "bg-amber-100 text-amber-700 border-amber-200"}>
                    {event.approved ? "Approved" : "Pending"}
                  </Badge>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-3 text-sm text-gray-500">
                  <div>
                    <p>Date: {new Date(event.date).toLocaleDateString()}</p>
                    <p>Time: {event.startTime} - {event.endTime}</p>
                  </div>
                  <div>
                    <p>Location: {event.location}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-gray-500 text-center py-8">No events for this club yet.</div>
          )}
        </div>
      </div>
    </div>
  );
}
