'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Users,
  Calendar,
  Building2,
  MoreVertical,
  Plus,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Phone,
  Mail,
  X,
  TrendingUp,
  Activity,
  Grid3X3,
  List
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import Image from 'next/image';

interface Club {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  eventCount: number;
  status: 'active' | 'inactive';
  createdAt: string;
  contactEmail: string;
  contactPhone: string;
  location: string;
  clubLeaderId?: string;
  category?: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  tags?: string[];
  lastActivity?: string;
  totalHours?: number;
}

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export default function ClubsPage() {
  const { user, token } = useAuth();
  const router = useRouter();
  const [clubs, setClubs] = useState<Club[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedClubLeader, setSelectedClubLeader] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showClubModal, setShowClubModal] = useState(false);
  const [selectedClub, setSelectedClub] = useState<Club | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'members' | 'events' | 'created'>('name');

  const fetchClubs = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/clubs', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch clubs');
      const data = await response.json();

      // Enhance club data with mock additional fields for demo
      const categories = ['Academic', 'Sports', 'Arts', 'Technology', 'Community Service', 'Cultural'];
      const enhancedClubs = data.map((club: Club) => ({
        ...club,
        category: club.category || categories[Math.floor(Math.random() * categories.length)],
        website: club.website || `https://${club.name.toLowerCase().replace(/\s+/g, '')}.club.com`,
        tags: club.tags || ['community', 'students', 'activities'],
        lastActivity: club.lastActivity || new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        totalHours: club.totalHours || Math.floor(Math.random() * 500) + 50,
        socialMedia: club.socialMedia || {
          facebook: `https://facebook.com/${club.name.toLowerCase().replace(/\s+/g, '')}`,
          instagram: `https://instagram.com/${club.name.toLowerCase().replace(/\s+/g, '')}`
        }
      }));

      setClubs(enhancedClubs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch users');
      const data = await response.json();
      const clubUsers = data.filter((user: User) => user.role === 'club');
      setUsers(clubUsers);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleCreateClub = async (clubData: Omit<Club, 'id' | 'memberCount' | 'eventCount' | 'createdAt'>) => {
    try {
      console.log('Token:', token);
      console.log('User ID:', user?.id);
      console.log('User Role:', user?.role);
      const response = await fetch('http://localhost:3001/api/clubs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'user-id': user?.id || '',
          'user-role': user?.role || ''
        },
        body: JSON.stringify({
          ...clubData,
          leaderId: selectedClubLeader?.id,
          status: 'active', // Explicitly setting status as 'active'
        })
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Failed to create club: ${errorData.message || response.statusText}`);
      }
      await fetchClubs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleUpdateClub = async (clubId: string, clubData: Partial<Club>) => {
    try {
      const response = await fetch(`http://localhost:3001/api/clubs/${clubId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(clubData)
      });
      if (!response.ok) throw new Error('Failed to update club');
      await fetchClubs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleStatusChange = async (clubId: string, newStatus: 'active' | 'inactive') => {
    try {
      const response = await fetch(`http://localhost:3001/api/admin/clubs/${clubId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) throw new Error('Failed to update club status');
      
      // Refresh clubs list
      await fetchClubs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleDeleteClub = async (clubId: string) => {
    // TODO: Implement club deletion logic
    console.log('Delete club:', clubId);
  };

  useEffect(() => {
    if (token) {
      fetchClubs();
      fetchUsers();
    }
  }, [token]);

  const handleViewClub = (club: Club) => {
    setSelectedClub(club);
    setShowClubModal(true);
  };

  const formatLastActivity = (lastActivity?: string) => {
    if (!lastActivity) return 'No recent activity';
    const date = new Date(lastActivity);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    return date.toLocaleDateString();
  };

  const getCategoryColor = (category?: string) => {
    switch (category?.toLowerCase()) {
      case 'academic': return 'bg-blue-100 text-blue-800';
      case 'sports': return 'bg-green-100 text-green-800';
      case 'arts': return 'bg-purple-100 text-purple-800';
      case 'technology': return 'bg-indigo-100 text-indigo-800';
      case 'community service': return 'bg-orange-100 text-orange-800';
      case 'cultural': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const sortedAndFilteredClubs = clubs
    .filter(club => {
      const matchesSearch = club.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           club.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (club.category?.toLowerCase() || '').includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || club.status === statusFilter;
      const matchesCategory = categoryFilter === 'all' || club.category === categoryFilter;
      return matchesSearch && matchesStatus && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'members': return b.memberCount - a.memberCount;
        case 'events': return b.eventCount - a.eventCount;
        case 'created': return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'name':
        default: return a.name.localeCompare(b.name);
      }
    });

  return (
    <div className="max-w-7xl mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center mb-2">
            <Building2 className="h-8 w-8 mr-3 text-gray-700" />
            Clubs Management
          </h1>
          <p className="text-gray-600">Manage and oversee all clubs in the system</p>
        </div>
        <div className="mt-4 lg:mt-0 flex flex-wrap gap-3">
          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-[#2c3e50] hover:bg-[#34495e] text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Club
          </Button>
          <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Clubs</p>
                <p className="text-2xl font-bold text-blue-900">{clubs.length}</p>
              </div>
              <Building2 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">Active Clubs</p>
                <p className="text-2xl font-bold text-green-900">
                  {clubs.filter(c => c.status === 'active').length}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Members</p>
                <p className="text-2xl font-bold text-purple-900">
                  {clubs.reduce((sum, club) => sum + club.memberCount, 0)}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Total Events</p>
                <p className="text-2xl font-bold text-orange-900">
                  {clubs.reduce((sum, club) => sum + club.eventCount, 0)}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-white border-gray-200">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search clubs by name, description, or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:border-blue-500"
                />
              </div>
              <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as 'all' | 'active' | 'inactive')}>
                <SelectTrigger className="w-full sm:w-[180px] bg-white border border-gray-300">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-[180px] bg-white border border-gray-300">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="Academic">Academic</SelectItem>
                  <SelectItem value="Sports">Sports</SelectItem>
                  <SelectItem value="Arts">Arts</SelectItem>
                  <SelectItem value="Technology">Technology</SelectItem>
                  <SelectItem value="Community Service">Community Service</SelectItem>
                  <SelectItem value="Cultural">Cultural</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={(value) => setSortBy(value as 'name' | 'members' | 'events' | 'created')}>
                <SelectTrigger className="w-full sm:w-[180px] bg-white border border-gray-300">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="members">Members</SelectItem>
                  <SelectItem value="events">Events</SelectItem>
                  <SelectItem value="created">Created Date</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="px-3 py-1"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="px-3 py-1"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center text-red-700">
              <X className="h-5 w-5 mr-2" />
              <span>Error: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="bg-white border-gray-200">
              <div className="animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-6 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  <div className="flex gap-4 mt-4">
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : sortedAndFilteredClubs.length === 0 ? (
        <Card className="bg-white border-gray-200">
          <CardContent className="p-8 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No clubs found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filter criteria.</p>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-[#2c3e50] hover:bg-[#34495e] text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create First Club
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className={viewMode === 'grid' ? 'grid gap-6 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}>
          {sortedAndFilteredClubs.map((club) =>
            viewMode === 'grid' ? (
                <Card key={club.id} className="bg-white border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group">
                  <div className="relative w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <Image
                      src="/placeholder-club.png"
                      alt={club.name}
                      layout="fill"
                      objectFit="cover"
                      className="group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-3 left-3">
                      <Badge className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(club.category)}`}>
                        {club.category}
                      </Badge>
                    </div>
                    <div className="absolute top-3 right-3">
                      <Badge className={`px-2 py-1 rounded-full text-xs font-medium ${
                        club.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {club.status}
                      </Badge>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <div className="mb-3">
                      <h3 className="text-lg font-bold text-gray-900 mb-1">{club.name}</h3>
                      <p className="text-sm text-gray-600 line-clamp-2">{club.description}</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div className="flex items-center text-gray-600">
                        <Users className="h-4 w-4 mr-2 text-blue-500" />
                        <span>{club.memberCount} Members</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Calendar className="h-4 w-4 mr-2 text-green-500" />
                        <span>{club.eventCount} Events</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <MapPin className="h-4 w-4 mr-2 text-orange-500" />
                        <span className="truncate">{club.location}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Activity className="h-4 w-4 mr-2 text-purple-500" />
                        <span>{formatLastActivity(club.lastActivity)}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewClub(club)}
                          className="text-xs"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/administrator/clubs/${club.id}`)}
                          className="text-xs"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewClub(club)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Club
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="h-4 w-4 mr-2" />
                            Contact Club
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Club
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                // List view
                <Card key={club.id} className="bg-white border-gray-200 hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 flex-1">
                        <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                          <Building2 className="h-8 w-8 text-gray-500" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-lg font-semibold text-gray-900">{club.name}</h3>
                            <Badge className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(club.category)}`}>
                              {club.category}
                            </Badge>
                            <Badge className={`px-2 py-1 rounded-full text-xs ${
                              club.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {club.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2 line-clamp-1">{club.description}</p>
                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <span className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              {club.memberCount} Members
                            </span>
                            <span className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {club.eventCount} Events
                            </span>
                            <span className="flex items-center">
                              <MapPin className="h-4 w-4 mr-1" />
                              {club.location}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewClub(club)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewClub(club)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Club
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Mail className="h-4 w-4 mr-2" />
                              Contact Club
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Club
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
          )}
        </div>
      )}

      {/* Club Details Modal */}
      {showClubModal && selectedClub && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Club Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowClubModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="p-6 space-y-6">
              {/* Club Header */}
              <div className="flex items-start space-x-6">
                <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                  <Building2 className="h-12 w-12 text-gray-500" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-2xl font-bold text-gray-900">{selectedClub.name}</h3>
                    <Badge className={`px-3 py-1 rounded-full text-sm ${getCategoryColor(selectedClub.category)}`}>
                      {selectedClub.category}
                    </Badge>
                    <Badge className={`px-3 py-1 rounded-full text-sm ${
                      selectedClub.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {selectedClub.status}
                    </Badge>
                  </div>
                  <p className="text-gray-600 mb-4">{selectedClub.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedClub.tags?.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-blue-900">{selectedClub.memberCount}</p>
                  <p className="text-sm text-blue-700">Members</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <Calendar className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-green-900">{selectedClub.eventCount}</p>
                  <p className="text-sm text-green-700">Events</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <TrendingUp className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-purple-900">{selectedClub.totalHours || 0}</p>
                  <p className="text-sm text-purple-700">Total Hours</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center">
                  <Activity className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                  <p className="text-sm font-medium text-orange-900">{formatLastActivity(selectedClub.lastActivity)}</p>
                  <p className="text-sm text-orange-700">Last Activity</p>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">Contact Information</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-gray-400" />
                      <span className="text-sm text-gray-900">{selectedClub.contactEmail}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <span className="text-sm text-gray-900">{selectedClub.contactPhone}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-gray-400" />
                      <span className="text-sm text-gray-900">{selectedClub.location}</span>
                    </div>
                    {selectedClub.website && (
                      <div className="flex items-center space-x-3">
                        <Eye className="h-5 w-5 text-gray-400" />
                        <a
                          href={selectedClub.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {selectedClub.website}
                        </a>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-gray-900">Club Information</h4>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Club ID</label>
                      <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded">{selectedClub.id}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Created Date</label>
                      <p className="text-sm text-gray-900">{new Date(selectedClub.createdAt).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                      <p className="text-sm text-gray-900">{selectedClub.category}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <Button variant="outline" onClick={() => setShowClubModal(false)}>
                  Close
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push(`/dashboard/administrator/clubs/${selectedClub.id}`)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Club
                </Button>
                <Button className="bg-[#2c3e50] hover:bg-[#34495e] text-white">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Club
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Club Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-slate-700/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-auto">
            <h2 className="text-2xl font-bold mb-4 text-gray-800">Create New Club</h2>
            <form onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              const newClub = {
                name: formData.get('name') as string,
                description: formData.get('description') as string,
                contactEmail: formData.get('contactEmail') as string,
                contactPhone: formData.get('contactPhone') as string,
                location: formData.get('location') as string,
                status: 'active' as 'active' | 'inactive', // Explicitly cast to the correct literal type
              };
              await handleCreateClub(newClub);
              setShowCreateModal(false);
            }} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Club Name</label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none bg-white text-gray-900"
                />
              </div>
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none bg-white text-gray-900"
                ></textarea>
              </div>
              <div>
                <label htmlFor="leader" className="block text-sm font-medium text-gray-700 mb-1">Club Leader (Optional)</label>
                <Select onValueChange={(value) => setSelectedClubLeader(users.find(u => u.id === value) || null)}>
                  <SelectTrigger className="w-full p-2 border border-gray-300 rounded-md focus:outline-none text-gray-800 bg-white">
                    <SelectValue placeholder="Select a club leader" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.length > 0 ? (
                      users.map((u) => (
                        <SelectItem key={u.id} value={u.id}>{u.name} ({u.email})</SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-users" disabled>No club leaders available</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
                <Input
                  id="contactEmail"
                  name="contactEmail"
                  type="email"
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none bg-white text-gray-900"
                />
              </div>
              <div>
                <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700 mb-1">Contact Phone</label>
                <Input
                  id="contactPhone"
                  name="contactPhone"
                  type="tel"
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none bg-white text-gray-900"
                />
              </div>
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">Club Room/Location</label>
                <Input
                  id="location"
                  name="location"
                  type="text"
                  required
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none bg-white text-gray-900"
                />
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <Button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  Create Club
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}