'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Users,
  Calendar,
  Building2,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  FileText,
  Settings,
  Activity,
  ArrowUpRight,
  Eye
} from 'lucide-react';

interface DashboardStats {
  totalUsers: number;
  totalClubs: number;
  totalEvents: number;
  pendingEvents: number;
  recentUsers?: number;
  recentClubs?: number;
  recentEvents?: number;
  approvedEvents?: number;
}

interface RecentActivity {
  id: string;
  type: 'user_joined' | 'club_created' | 'event_submitted' | 'event_approved';
  title: string;
  description: string;
  timestamp: string;
  user?: {
    name: string;
    role: string;
  };
}

export default function AdminDashboard() {
  const { user, token } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        const response = await fetch('http://localhost:3001/api/admin/dashboard', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) throw new Error('Failed to fetch dashboard stats');

        const data = await response.json();
        setStats(data.stats);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    }

    async function fetchRecentActivity() {
      try {
        // Mock recent activity data for now - in real app this would be an API call
        const mockActivity: RecentActivity[] = [
          {
            id: '1',
            type: 'user_joined',
            title: 'New User Registration',
            description: 'John Doe joined as a student',
            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
            user: { name: 'John Doe', role: 'student' }
          },
          {
            id: '2',
            type: 'event_submitted',
            title: 'Event Submitted for Approval',
            description: 'Tech Club submitted "Coding Workshop"',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
            user: { name: 'Tech Club', role: 'club' }
          },
          {
            id: '3',
            type: 'club_created',
            title: 'New Club Created',
            description: 'Environmental Club was created',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
            user: { name: 'Admin', role: 'admin' }
          },
          {
            id: '4',
            type: 'event_approved',
            title: 'Event Approved',
            description: 'Community Service Day was approved',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),
            user: { name: 'Admin', role: 'admin' }
          }
        ];
        setRecentActivity(mockActivity);
      } catch (err) {
        console.error('Failed to fetch recent activity:', err);
      } finally {
        setActivityLoading(false);
      }
    }

    if (token) {
      fetchStats();
      fetchRecentActivity();
    }
  }, [token]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-700 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="max-w-7xl mx-auto py-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <p className="text-red-700">Error: {error}</p>
          </div>
        </div>
      </div>
    );
  }
  if (!stats) return null;

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'user_joined': return <Users className="h-4 w-4 text-blue-500" />;
      case 'club_created': return <Building2 className="h-4 w-4 text-green-500" />;
      case 'event_submitted': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'event_approved': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <div className="max-w-7xl mx-auto py-6 space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center mb-2">
            <Building2 className="h-8 w-8 mr-3 text-gray-700" />
            Administrator Dashboard
          </h1>
          <p className="text-gray-600">Welcome back! Here's what's happening in your system.</p>
        </div>
        <div className="mt-4 lg:mt-0 flex flex-wrap gap-3">
          <Button className="bg-[#2c3e50] hover:bg-[#34495e] text-white">
            <Plus className="h-4 w-4 mr-2" />
            Quick Actions
          </Button>
          <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50">
            <FileText className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Total Users</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg group-hover:bg-blue-600 transition-colors">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{stats.totalUsers}</div>
            <div className="flex items-center text-xs text-blue-600 mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>+12% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Total Clubs</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg group-hover:bg-green-600 transition-colors">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{stats.totalClubs}</div>
            <div className="flex items-center text-xs text-green-600 mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>+8% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Total Events</CardTitle>
            <div className="p-2 bg-purple-500 rounded-lg group-hover:bg-purple-600 transition-colors">
              <Calendar className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{stats.totalEvents}</div>
            <div className="flex items-center text-xs text-purple-600 mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              <span>+15% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300 group">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Pending Events</CardTitle>
            <div className="p-2 bg-orange-500 rounded-lg group-hover:bg-orange-600 transition-colors">
              <Clock className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900">{stats.pendingEvents}</div>
            <div className="flex items-center text-xs text-orange-600 mt-1">
              <AlertCircle className="h-3 w-3 mr-1" />
              <span>Requires attention</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Quick Actions */}
        <div className="lg:col-span-1">
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                <Settings className="h-5 w-5 mr-2 text-gray-600" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/dashboard/administrator/users">
                <Button variant="outline" className="w-full justify-start border-gray-200 hover:bg-gray-50">
                  <Users className="h-4 w-4 mr-2 text-blue-500" />
                  Manage Users
                  <ArrowUpRight className="h-3 w-3 ml-auto text-gray-400" />
                </Button>
              </Link>
              <Link href="/dashboard/administrator/clubs">
                <Button variant="outline" className="w-full justify-start border-gray-200 hover:bg-gray-50">
                  <Building2 className="h-4 w-4 mr-2 text-green-500" />
                  Manage Clubs
                  <ArrowUpRight className="h-3 w-3 ml-auto text-gray-400" />
                </Button>
              </Link>
              <Link href="/dashboard/administrator/events">
                <Button variant="outline" className="w-full justify-start border-gray-200 hover:bg-gray-50">
                  <Calendar className="h-4 w-4 mr-2 text-purple-500" />
                  Review Events
                  <ArrowUpRight className="h-3 w-3 ml-auto text-gray-400" />
                </Button>
              </Link>
              <Link href="/dashboard/administrator/reports">
                <Button variant="outline" className="w-full justify-start border-gray-200 hover:bg-gray-50">
                  <FileText className="h-4 w-4 mr-2 text-orange-500" />
                  Generate Reports
                  <ArrowUpRight className="h-3 w-3 ml-auto text-gray-400" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                <Activity className="h-5 w-5 mr-2 text-gray-600" />
                Recent Activity
              </CardTitle>
              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                <Eye className="h-4 w-4 mr-1" />
                View All
              </Button>
            </CardHeader>
            <CardContent>
              {activityLoading ? (
                <div className="space-y-3">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="animate-pulse flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex-shrink-0 mt-0.5">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                        <p className="text-sm text-gray-600">{activity.description}</p>
                        <div className="flex items-center mt-1 text-xs text-gray-500">
                          <span>{formatTimeAgo(activity.timestamp)}</span>
                          {activity.user && (
                            <>
                              <span className="mx-1">•</span>
                              <span>{activity.user.name}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}