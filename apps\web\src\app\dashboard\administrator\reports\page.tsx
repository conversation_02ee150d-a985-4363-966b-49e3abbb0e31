'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileText,
  CalendarDays,
  Download,
  BarChart3,
  PieChart,
  TrendingUp,
  Users,
  Building2,
  Calendar,
  Clock,
  Filter,
  Search,
  Eye,
  Trash2,
  Plus,
  X,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

interface Report {
  id: string;
  type: string;
  generatedBy: {
    id: string;
    name: string;
  };
  createdAt: string;
  url: string;
  status?: 'generating' | 'completed' | 'failed';
  size?: string;
  description?: string;
  filters?: {
    startDate?: string;
    endDate?: string;
    clubs?: string[];
    categories?: string[];
  };
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  fields: string[];
}

export default function ReportsPage() {
  const { user, token } = useAuth();
  const router = useRouter();
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportType, setReportType] = useState<string>('');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });
  const [selectedClubs, setSelectedClubs] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [showPreview, setShowPreview] = useState(false);
  const [generating, setGenerating] = useState(false);

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'event_summary',
      name: 'Event Summary',
      description: 'Comprehensive overview of all events, attendance, and performance metrics',
      icon: Calendar,
      color: 'bg-blue-500',
      fields: ['Event Details', 'Attendance', 'Club Performance', 'Date Range Analysis']
    },
    {
      id: 'attendance',
      name: 'Attendance Report',
      description: 'Detailed attendance tracking across all events and clubs',
      icon: Users,
      color: 'bg-green-500',
      fields: ['Member Attendance', 'Event Participation', 'Trends', 'Club Comparison']
    },
    {
      id: 'hours',
      name: 'Service Hours',
      description: 'Community service hours tracking and volunteer contribution analysis',
      icon: Clock,
      color: 'bg-purple-500',
      fields: ['Total Hours', 'Individual Contributions', 'Club Rankings', 'Monthly Trends']
    },
    {
      id: 'club_performance',
      name: 'Club Performance',
      description: 'Club activity analysis, member engagement, and growth metrics',
      icon: Building2,
      color: 'bg-orange-500',
      fields: ['Member Growth', 'Event Frequency', 'Engagement Metrics', 'Performance Scores']
    },
    {
      id: 'financial',
      name: 'Financial Summary',
      description: 'Budget tracking, expenses, and financial performance overview',
      icon: BarChart3,
      color: 'bg-indigo-500',
      fields: ['Budget Allocation', 'Expenses', 'Revenue', 'Cost Analysis']
    },
    {
      id: 'custom',
      name: 'Custom Report',
      description: 'Build your own report with custom fields and filters',
      icon: PieChart,
      color: 'bg-pink-500',
      fields: ['Custom Fields', 'Advanced Filters', 'Data Export', 'Visualization Options']
    }
  ];

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      // Mock reports data for demo
      const mockReports: Report[] = [
        {
          id: '1',
          type: 'Event Summary',
          generatedBy: { id: '1', name: 'Admin User' },
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2).toISOString(),
          url: '/reports/event-summary-2024.pdf',
          status: 'completed',
          size: '2.4 MB',
          description: 'Q1 2024 Event Summary Report',
          filters: {
            startDate: '2024-01-01',
            endDate: '2024-03-31'
          }
        },
        {
          id: '2',
          type: 'Attendance Report',
          generatedBy: { id: '1', name: 'Admin User' },
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7).toISOString(),
          url: '/reports/attendance-march-2024.pdf',
          status: 'completed',
          size: '1.8 MB',
          description: 'March 2024 Attendance Analysis'
        },
        {
          id: '3',
          type: 'Service Hours',
          generatedBy: { id: '1', name: 'Admin User' },
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14).toISOString(),
          url: '/reports/service-hours-2024.pdf',
          status: 'completed',
          size: '3.1 MB',
          description: 'YTD Service Hours Report'
        }
      ];
      setReports(mockReports);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async () => {
    try {
      setGenerating(true);
      setError(null);

      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newReport: Report = {
        id: Date.now().toString(),
        type: reportTemplates.find(t => t.id === reportType)?.name || reportType,
        generatedBy: { id: '1', name: 'Admin User' },
        createdAt: new Date().toISOString(),
        url: `/reports/${reportType}-${Date.now()}.pdf`,
        status: 'completed',
        size: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
        description: `Generated ${new Date().toLocaleDateString()} report`,
        filters: {
          startDate: dateRange.start,
          endDate: dateRange.end,
          clubs: selectedClubs,
          categories: selectedCategories
        }
      };

      setReports(prev => [newReport, ...prev]);

      // Reset form
      setReportType('');
      setDateRange({ start: '', end: '' });
      setSelectedClubs([]);
      setSelectedCategories([]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setGenerating(false);
    }
  };

  const getReportIcon = (type: string) => {
    const template = reportTemplates.find(t => t.name === type || t.id === type);
    if (template) {
      const Icon = template.icon;
      return <Icon className="h-5 w-5" />;
    }
    return <FileText className="h-5 w-5" />;
  };

  const getReportColor = (type: string) => {
    const template = reportTemplates.find(t => t.name === type || t.id === type);
    return template?.color || 'bg-gray-500';
  };

  const formatFileSize = (size?: string) => {
    return size || 'Unknown size';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.generatedBy.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || report.type.toLowerCase().includes(filterType.toLowerCase());
    return matchesSearch && matchesType;
  });

  return (
    <div className="max-w-7xl mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center mb-2">
            <FileText className="h-8 w-8 mr-3 text-gray-700" />
            Reports & Analytics
          </h1>
          <p className="text-gray-600">Generate comprehensive reports and analyze system data</p>
        </div>
        <div className="mt-4 lg:mt-0 flex flex-wrap gap-3">
          <Button className="bg-[#2c3e50] hover:bg-[#34495e] text-white">
            <Plus className="h-4 w-4 mr-2" />
            Quick Report
          </Button>
          <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export All
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Reports</p>
                <p className="text-2xl font-bold text-blue-900">{reports.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">This Month</p>
                <p className="text-2xl font-bold text-green-900">
                  {reports.filter(r => {
                    const reportDate = new Date(r.createdAt);
                    const now = new Date();
                    return reportDate.getMonth() === now.getMonth() && reportDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Templates</p>
                <p className="text-2xl font-bold text-purple-900">{reportTemplates.length}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-700">Data Size</p>
                <p className="text-2xl font-bold text-orange-900">
                  {reports.reduce((total, report) => {
                    const size = parseFloat(report.size?.replace(' MB', '') || '0');
                    return total + size;
                  }, 0).toFixed(1)} MB
                </p>
              </div>
              <PieChart className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error State */}
      {error && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center text-red-700">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>Error: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Templates */}
      <Card className="bg-white border-gray-200">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-gray-900">Report Templates</CardTitle>
          <p className="text-gray-600">Choose from pre-built report templates or create a custom report</p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {reportTemplates.map((template) => {
              const Icon = template.icon;
              return (
                <div
                  key={template.id}
                  onClick={() => setReportType(template.id)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 hover:shadow-md ${
                    reportType === template.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${template.color} text-white`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                      <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {template.fields.slice(0, 2).map((field, index) => (
                          <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            {field}
                          </span>
                        ))}
                        {template.fields.length > 2 && (
                          <span className="text-xs text-gray-500">+{template.fields.length - 2} more</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Report Configuration */}
      {reportType && (
        <Card className="bg-white border-gray-200">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-gray-900">Configure Report</CardTitle>
            <p className="text-gray-600">Set up filters and parameters for your report</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                <Input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                <Input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="w-full"
                />
              </div>
            </div>

            {/* Report Preview */}
            {reportType && dateRange.start && dateRange.end && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Report Preview</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Type:</strong> {reportTemplates.find(t => t.id === reportType)?.name}</p>
                  <p><strong>Period:</strong> {new Date(dateRange.start).toLocaleDateString()} - {new Date(dateRange.end).toLocaleDateString()}</p>
                  <p><strong>Estimated Size:</strong> {(Math.random() * 3 + 1).toFixed(1)} MB</p>
                  <p><strong>Generation Time:</strong> ~2-3 minutes</p>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setReportType('')}
                className="border-gray-300 text-gray-700"
              >
                Cancel
              </Button>
              <Button
                onClick={handleGenerateReport}
                disabled={!reportType || !dateRange.start || !dateRange.end || generating}
                className="bg-[#2c3e50] hover:bg-[#34495e] text-white"
              >
                {generating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      {/* Report History */}
      <Card className="bg-white border-gray-200">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold text-gray-900">Report History</CardTitle>
            <p className="text-gray-600">View and manage previously generated reports</p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-64"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="event">Event Reports</SelectItem>
                <SelectItem value="attendance">Attendance</SelectItem>
                <SelectItem value="hours">Service Hours</SelectItem>
                <SelectItem value="club">Club Reports</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 h-32 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || filterType !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'Generate your first report to get started.'
                }
              </p>
              {!searchTerm && filterType === 'all' && (
                <Button
                  onClick={() => setReportType('event_summary')}
                  className="bg-[#2c3e50] hover:bg-[#34495e] text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Generate First Report
                </Button>
              )}
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredReports.map((report) => (
                <Card key={report.id} className="border border-gray-200 hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <div className={`p-2 rounded-lg ${getReportColor(report.type)} text-white`}>
                        {getReportIcon(report.type)}
                      </div>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(report.url, '_blank')}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(report.url, '_blank')}
                          className="h-8 w-8 p-0"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <h3 className="font-semibold text-gray-900 mb-1">{report.type}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {report.description || 'No description available'}
                    </p>

                    <div className="space-y-2 text-xs text-gray-500">
                      <div className="flex items-center justify-between">
                        <span>Generated:</span>
                        <span>{formatDate(report.createdAt)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Size:</span>
                        <span>{formatFileSize(report.size)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>By:</span>
                        <span>{report.generatedBy.name}</span>
                      </div>
                      {report.status && (
                        <div className="flex items-center justify-between">
                          <span>Status:</span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            report.status === 'completed' ? 'bg-green-100 text-green-800' :
                            report.status === 'generating' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {report.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                            {report.status}
                          </span>
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={() => window.open(report.url, '_blank')}
                      className="w-full mt-4 bg-[#2c3e50] hover:bg-[#34495e] text-white"
                      size="sm"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Report
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
