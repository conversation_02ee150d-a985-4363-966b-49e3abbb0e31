'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Building2,
  AlertCircle,
  Edit2,
  Trash2,
  User<PERSON><PERSON>ck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  capacity: number;
  registrations?: number;
  approved: boolean;
  clubId: string;
  club?: {
    id: string;
    name: string;
    description: string;
  };
}

interface Registration {
  id: string;
  userId: string;
  eventId: string;
  registeredAt: string;
  attended: boolean;
}

interface ClubProfile {
  id: string;
  name: string;
  description: string;
  leaderId: string;
}

export default function EventDetails({ params }: { params: { id: string } }) {
  const { user, token } = useAuth();
  const router = useRouter();
  const [event, setEvent] = useState<Event | null>(null);
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [clubProfile, setClubProfile] = useState<ClubProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchEventData();
      fetchClubProfile();
    }
  }, [user?.id, params.id]);

  const fetchEventData = async () => {
    try {
      // Fetch event details
      const eventResponse = await fetch(`http://localhost:3001/api/events/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'user-id': user?.id || ''
        }
      });
      
      if (!eventResponse.ok) throw new Error('Failed to fetch event details');
      const eventData = await eventResponse.json();
      setEvent(eventData);

      // Fetch registrations
      const registrationsResponse = await fetch(`http://localhost:3001/api/registrations/event/${params.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'user-id': user?.id || ''
        }
      });
      
      if (!registrationsResponse.ok) throw new Error('Failed to fetch registrations');
      const registrationsData = await registrationsResponse.json();
      setRegistrations(registrationsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchClubProfile = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/profile/club', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'user-id': user?.id || ''
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch club profile');
      
      const data = await response.json();
      setClubProfile(data);
    } catch (err) {
      console.error('Error fetching club profile:', err);
    }
  };

  const isClubLeader = clubProfile && clubProfile.leaderId === user?.id && event?.clubId === clubProfile.id;

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded-lg"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-red-500 mb-4">Error: {error}</p>
            <div className="flex gap-2">
              <Button onClick={() => router.back()}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Event Not Found</h1>
            <p className="text-gray-600 mb-4">The event you're looking for doesn't exist or has been removed.</p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="shrink-0"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{event.title}</h1>
            <div className="flex items-center gap-2 mt-1">
              <Building2 className="w-4 h-4 text-gray-500" />
              <span className="text-gray-600">{clubProfile?.name || 'Club Event'}</span>
              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                event.approved
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {event.approved ? (
                  <>
                    <CheckCircle className="w-3 h-3" />
                    Approved
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-3 h-3" />
                    Pending
                  </>
                )}
              </div>
            </div>
          </div>
        </div>

        {isClubLeader && (
          <div className="flex gap-2">
            <Link href={`/dashboard/club/manage`}>
              <Button variant="outline" size="sm">
                <Edit2 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Link href={`/dashboard/club/manage/participants/${event.id}`}>
              <Button variant="outline" size="sm">
                <UserCheck className="w-4 h-4 mr-2" />
                Participants
              </Button>
            </Link>
          </div>
        )}
      </div>

      {/* Event Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Event Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{event.description}</p>
            </CardContent>
          </Card>

          {/* Organizer Info */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="w-5 h-5 text-[#7a8c9e]" />
                Organizer
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500">Club Name</p>
                  <p className="font-medium text-gray-900">{clubProfile?.name || 'Loading...'}</p>
                </div>
                {clubProfile?.description && (
                  <div>
                    <p className="text-sm text-gray-500">About</p>
                    <p className="text-gray-700">{clubProfile.description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Registrations */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-[#7a8c9e]" />
                Registrations ({registrations.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {registrations.length > 0 ? (
                <div className="space-y-3">
                  {registrations.map((registration) => (
                    <div key={registration.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium text-gray-900">User ID: {registration.userId}</p>
                          <p className="text-sm text-gray-500">
                            Registered: {new Date(registration.registeredAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {registration.attended ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <XCircle className="w-5 h-5 text-yellow-600" />
                          )}
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            registration.attended
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {registration.attended ? 'Attended' : 'Not Attended'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No registrations yet</h3>
                  <p className="text-gray-600">Registrations will appear here once people sign up</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-[#7a8c9e]" />
                Event Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-[#7a8c9e]" />
                <div>
                  <p className="text-sm text-gray-500">Date</p>
                  <p className="font-medium">{new Date(event.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-[#7a8c9e]" />
                <div>
                  <p className="text-sm text-gray-500">Time</p>
                  <p className="font-medium">{event.startTime} - {event.endTime}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <MapPin className="w-5 h-5 text-[#7a8c9e]" />
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p className="font-medium">{event.location}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Users className="w-5 h-5 text-[#7a8c9e]" />
                <div>
                  <p className="text-sm text-gray-500">Capacity</p>
                  <p className="font-medium">{event.registrations || 0} / {event.capacity} registered</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Registration Progress */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-[#7a8c9e]" />
                Registration Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Registered</span>
                  <span>{event.registrations || 0} / {event.capacity}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-[#7a8c9e] to-[#a8a4c5] h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(((event.registrations || 0) / event.capacity) * 100, 100)}%`
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">
                  {event.capacity - (event.registrations || 0)} spots remaining
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 