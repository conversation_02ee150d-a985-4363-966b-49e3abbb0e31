'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, UserCheck, XCircle, Users, User, Calendar, CheckCircle, AlertCircle } from 'lucide-react';

interface Participant {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  registeredAt: string;
  attended: boolean;
}

export default function EventParticipants({ params }: { params: { eventId: string } }) {
  const { user, token } = useAuth();
  const router = useRouter();
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [eventDetails, setEventDetails] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch event details
        const eventResponse = await fetch(`http://localhost:3001/api/events/${params.eventId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-User-ID': user?.id || '',
            'X-User-Role': user?.role || ''
          }
        });
        
        if (!eventResponse.ok) throw new Error('Failed to fetch event details');
        const eventData = await eventResponse.json();
        setEventDetails(eventData);

        // Fetch participants
        const participantsResponse = await fetch(`http://localhost:3001/api/registrations/event/${params.eventId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'X-User-ID': user?.id || '',
            'X-User-Role': user?.role || ''
          }
        });
        
        if (!participantsResponse.ok) throw new Error('Failed to fetch participants');
        const participantsData = await participantsResponse.json();
        setParticipants(participantsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.eventId, token, user]);

  const handleMarkAttendance = async (participantId: string, attended: boolean) => {
    try {
      const response = await fetch(`http://localhost:3001/api/registrations/${participantId}/attendance`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-User-ID': user?.id || '',
          'X-User-Role': user?.role || ''
        },
        body: JSON.stringify({ attended })
      });

      if (!response.ok) throw new Error('Failed to update attendance');

      // Update local state
      setParticipants(participants.map(p => 
        p.id === participantId ? { ...p, attended } : p
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border border-gray-200 rounded">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-32"></div>
                      <div className="h-3 bg-gray-200 rounded w-48"></div>
                    </div>
                  </div>
                  <div className="h-8 bg-gray-200 rounded w-24"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-red-500 mb-4">Error: {error}</p>
            <div className="flex gap-2">
              <Button onClick={() => router.back()}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Events
              </Button>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="shrink-0"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <UserCheck className="w-8 h-8 text-[#7a8c9e]" />
              Event Participants
            </h1>
            {eventDetails && (
              <div className="flex items-center gap-2 mt-1">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="text-gray-600">
                  {eventDetails.title} • {new Date(eventDetails.date).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Participants List */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5 text-[#7a8c9e]" />
            Participants ({participants.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {participants.length > 0 ? (
            <div className="space-y-4">
              {participants.map((participant) => (
                <div key={participant.id} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-[#7a8c9e] to-[#a8a4c5] rounded-full flex items-center justify-center">
                        <User className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{participant.user.name}</h3>
                        <p className="text-sm text-gray-500">{participant.user.email}</p>
                        <p className="text-xs text-gray-400">
                          Registered: {new Date(participant.registeredAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className={`flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                        participant.attended
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {participant.attended ? (
                          <>
                            <CheckCircle className="w-3 h-3" />
                            Attended
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-3 h-3" />
                            Not Attended
                          </>
                        )}
                      </div>

                      <Button
                        onClick={() => handleMarkAttendance(participant.id, !participant.attended)}
                        variant="outline"
                        size="sm"
                        className={participant.attended
                          ? 'text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200'
                          : 'text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200'
                        }
                      >
                        {participant.attended ? (
                          <>
                            <XCircle className="w-4 h-4 mr-2" />
                            Mark Absent
                          </>
                        ) : (
                          <>
                            <UserCheck className="w-4 h-4 mr-2" />
                            Mark Present
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No participants yet</h3>
              <p className="text-gray-600">Participants will appear here once people register for the event</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}