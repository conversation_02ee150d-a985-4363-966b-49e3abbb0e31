'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { BarChart2, Users, Clock, Calendar, TrendingUp, Activity, Target, Award } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ClubStats {
  clubId: string;
  totalEvents: number;
  totalAttendees: number;
  totalHours: number;
}

interface EventStats {
  eventId: string;
  totalRegistered: number;
  totalAttended: number;
  totalHours: number;
  attendanceRate: number;
}

interface Event {
  id: string;
  title: string;
  date: string;
}

export default function ClubStats() {
  const { user, token } = useAuth();
  const [clubStats, setClubStats] = useState<ClubStats | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<string>('');
  const [selectedEventStats, setSelectedEventStats] = useState<EventStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchClubStats();
      fetchEvents();
    }
  }, [user?.id]);

  const fetchClubStats = async () => {
    try {
      const response = await fetch(`http://localhost:3001/api/stats/club/${user?.id}`, {
        headers: {
          'user-id': user?.id || '',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch club stats');
      
      const data = await response.json();
      setClubStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchEvents = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/events/my', {
        headers: {
          'user-id': user?.id || '',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch events');
      
      const data = await response.json();
      setEvents(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const fetchEventStats = async (eventId: string) => {
    try {
      const response = await fetch(`http://localhost:3001/api/stats/event/${eventId}`, {
        headers: {
          'user-id': user?.id || '',
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch event stats');
      
      const data = await response.json();
      setSelectedEventStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleEventChange = (eventId: string) => {
    setSelectedEvent(eventId);
    if (eventId) {
      fetchEventStats(eventId);
    } else {
      setSelectedEventStats(null);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-red-500 mb-4">Error: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Try Again
            </button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          <BarChart2 className="w-8 h-8 text-[#7a8c9e]" />
          Club Statistics
        </h1>
        <p className="text-gray-600 mt-1">View your club's performance metrics and event statistics</p>
      </div>

      {/* Club Overview Stats */}
      {clubStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <h3 className="text-sm font-medium text-blue-600">Total Events</h3>
                  </div>
                  <p className="text-3xl font-bold text-blue-900">{clubStats.totalEvents}</p>
                  <p className="text-xs text-blue-700 mt-1">Events organized</p>
                </div>
                <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="w-5 h-5 text-green-600" />
                    <h3 className="text-sm font-medium text-green-600">Total Attendees</h3>
                  </div>
                  <p className="text-3xl font-bold text-green-900">{clubStats.totalAttendees}</p>
                  <p className="text-xs text-green-700 mt-1">People reached</p>
                </div>
                <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-5 h-5 text-purple-600" />
                    <h3 className="text-sm font-medium text-purple-600">Total Hours</h3>
                  </div>
                  <p className="text-3xl font-bold text-purple-900">{clubStats.totalHours}</p>
                  <p className="text-xs text-purple-700 mt-1">Hours of impact</p>
                </div>
                <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Event Statistics */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-[#7a8c9e]" />
            Event Statistics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Event</label>
            <select
              value={selectedEvent}
              onChange={(e) => handleEventChange(e.target.value)}
              className="w-full p-3 rounded-lg border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#7a8c9e] focus:border-transparent"
            >
              <option value="">Select an event to view detailed statistics</option>
              {events.map((event) => (
                <option key={event.id} value={event.id}>
                  {event.title} ({new Date(event.date).toLocaleDateString()})
                </option>
              ))}
            </select>
          </div>

          {/* Event Details */}
          {selectedEventStats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="border border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="w-4 h-4 text-blue-500" />
                    <p className="text-xs font-medium text-gray-600">Registered</p>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{selectedEventStats.totalRegistered}</p>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Users className="w-4 h-4 text-green-500" />
                    <p className="text-xs font-medium text-gray-600">Attended</p>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{selectedEventStats.totalAttended}</p>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-4 h-4 text-purple-500" />
                    <p className="text-xs font-medium text-gray-600">Attendance Rate</p>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{selectedEventStats.attendanceRate}%</p>
                </CardContent>
              </Card>

              <Card className="border border-gray-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-orange-500" />
                    <p className="text-xs font-medium text-gray-600">Total Hours</p>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{selectedEventStats.totalHours}</p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Select an Event</h3>
              <p className="text-gray-600">Choose an event from the dropdown to view detailed statistics</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


