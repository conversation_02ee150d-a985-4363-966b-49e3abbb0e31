'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import {
  CalendarDays,
  Search,
  FileText,
  Filter,
  SortAsc,
  SortDesc,
  Clock,
  MapPin,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download
} from 'lucide-react';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  club: {
    id: string;
    name: string;
    description: string;
  } | null;
  createdAt?: string;
}

interface Registration {
  id: string;
  eventId: string;
  event: Event;
  attended: boolean;
  hoursEarned: number | null;
}

export default function StudentEvents() {
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const router = useRouter();
  const { user, token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login/student');
      return;
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    const fetchRegistrations = async () => {
      if (!isAuthenticated || !user) return;

      try {
        const response = await fetch(`http://localhost:3001/api/registrations/user/${user.id}`, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'user-id': user.id
          }
        });
        
        if (!response.ok) throw new Error('Failed to fetch registrations');
        
        const data = await response.json();
        setRegistrations(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchRegistrations();
  }, [isAuthenticated, user, token]);

  const filteredAndSortedRegistrations = registrations
    .filter(reg => {
      const matchesSearch = reg.event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reg.event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reg.event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reg.event.club?.name.toLowerCase().includes(searchTerm.toLowerCase());

      if (filterStatus === 'all') return matchesSearch;

      const status = getRegistrationStatus(reg);
      return matchesSearch && status.toLowerCase() === filterStatus.toLowerCase();
    })
    .sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.event.date).getTime() - new Date(b.event.date).getTime();
          break;
        case 'title':
          comparison = a.event.title.localeCompare(b.event.title);
          break;
        case 'status':
          comparison = getRegistrationStatus(a).localeCompare(getRegistrationStatus(b));
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const getRegistrationStatus = (registration: Registration) => {
    if (registration.attended) {
      return 'Registered';
    } else {
      // This implies pending if it's in the list but not attended
      return 'Pending'; 
    }
  };

  const getAttendanceStatus = (registration: Registration) => {
    if (registration.attended) {
      return 'Verified';
    } else {
      return 'Not yet verified';
    }
  };

  if (!isAuthenticated || !user) {
    return <div className="text-center text-gray-700">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="h-10 bg-gray-200 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-64 mb-6 animate-pulse"></div>

          <div className="flex flex-col sm:flex-row gap-4">
            <div className="h-12 bg-gray-200 rounded-xl flex-1 animate-pulse"></div>
            <div className="h-12 w-32 bg-gray-200 rounded-xl animate-pulse"></div>
            <div className="h-12 w-32 bg-gray-200 rounded-xl animate-pulse"></div>
          </div>
        </div>

        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-4 animate-pulse"></div>
              <div className="space-y-2 mb-4">
                <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3 animate-pulse"></div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex space-x-3">
                  <div className="h-6 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                  <div className="h-6 w-20 bg-gray-200 rounded-full animate-pulse"></div>
                </div>
                <div className="h-8 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div>
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 flex items-center mb-2">
              <div className="p-2 bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab] rounded-xl mr-3">
                <CalendarDays className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
              </div>
              My Events
            </h1>
            <p className="text-gray-600 text-lg">Track your event registrations and attendance</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* Search Bar */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              type="text"
              placeholder="Search events..."
              className="pl-10 pr-4 py-3 border border-gray-300 rounded-xl w-full bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent placeholder-gray-400 shadow-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filter Dropdown */}
          <div className="relative">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="appearance-none bg-white border border-gray-300 rounded-xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent shadow-sm"
            >
              <option value="all">All Status</option>
              <option value="registered">Registered</option>
              <option value="pending">Pending</option>
            </select>
            <Filter className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Sort Dropdown */}
          <div className="relative">
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as 'date' | 'title' | 'status');
                setSortOrder(order as 'asc' | 'desc');
              }}
              className="appearance-none bg-white border border-gray-300 rounded-xl px-4 py-3 pr-10 focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent shadow-sm"
            >
              <option value="date-desc">Date (Newest)</option>
              <option value="date-asc">Date (Oldest)</option>
              <option value="title-asc">Title (A-Z)</option>
              <option value="title-desc">Title (Z-A)</option>
              <option value="status-asc">Status (A-Z)</option>
            </select>
            {sortOrder === 'asc' ? (
              <SortAsc className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            ) : (
              <SortDesc className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <p className="text-red-600 font-medium">Error: {error}</p>
        </div>
      )}

      {filteredAndSortedRegistrations.length === 0 && !loading && !error && (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CalendarDays className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">No events found</h3>
            <p className="text-gray-500">Try adjusting your search terms or filters.</p>
          </div>
        </div>
      )}

      {/* Events List - Mobile-First Design */}
      <div className="space-y-4">
        {filteredAndSortedRegistrations.map((registration) => (
          <div key={registration.id} className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-200">
            {/* Mobile Layout */}
            <div className="block lg:hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">{registration.event.title}</h3>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <CalendarDays className="h-4 w-4 mr-2" />
                        <span>{new Date(registration.event.date).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>{new Date(`2000-01-01T${registration.event.startTime}`).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        <span>{registration.event.location}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${
                      getRegistrationStatus(registration) === 'Registered' ? 'bg-green-100 text-green-700' :
                      getRegistrationStatus(registration) === 'Pending' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-red-100 text-red-700'
                    }`}>
                      {getRegistrationStatus(registration) === 'Registered' && <CheckCircle className="h-3 w-3 mr-1" />}
                      {getRegistrationStatus(registration) === 'Pending' && <AlertCircle className="h-3 w-3 mr-1" />}
                      {getRegistrationStatus(registration)}
                    </span>

                    <span className={`px-3 py-1 rounded-full text-xs font-medium flex items-center ${
                      getAttendanceStatus(registration) === 'Verified' ? 'bg-green-100 text-green-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {getAttendanceStatus(registration) === 'Verified' ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Verified
                        </>
                      ) : (
                        <>
                          <XCircle className="h-3 w-3 mr-1" />
                          Not Verified
                        </>
                      )}
                    </span>
                  </div>

                  <Link
                    href={`/dashboard/student/events/${registration.event.id}`}
                    className="px-4 py-2 bg-[#a8a4c5] text-white rounded-lg text-sm hover:bg-[#8e8aab] transition-colors flex items-center"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    Details
                  </Link>
                </div>
              </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden lg:block">
              <div className="grid grid-cols-12 items-center gap-4 p-6">
                {/* Event Info - 5 columns */}
                <div className="col-span-5">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{registration.event.title}</h3>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm text-gray-600">
                      <CalendarDays className="h-4 w-4 mr-2" />
                      <span>{new Date(registration.event.date).toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}</span>
                      <Clock className="h-4 w-4 ml-4 mr-1" />
                      <span>{new Date(`2000-01-01T${registration.event.startTime}`).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{registration.event.location}</span>
                    </div>
                  </div>
                </div>

                {/* Registration Status - 2 columns */}
                <div className="col-span-2 text-center">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center ${
                    getRegistrationStatus(registration) === 'Registered' ? 'bg-green-100 text-green-700' :
                    getRegistrationStatus(registration) === 'Pending' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-red-100 text-red-700'
                  }`}>
                    {getRegistrationStatus(registration) === 'Registered' && <CheckCircle className="h-3 w-3 mr-1" />}
                    {getRegistrationStatus(registration) === 'Pending' && <AlertCircle className="h-3 w-3 mr-1" />}
                    {getRegistrationStatus(registration)}
                  </span>
                </div>

                {/* Attendance Status - 2 columns */}
                <div className="col-span-2 text-center">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium inline-flex items-center ${
                    getAttendanceStatus(registration) === 'Verified' ? 'bg-green-100 text-green-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {getAttendanceStatus(registration) === 'Verified' ? (
                      <>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verified
                      </>
                    ) : (
                      <>
                        <XCircle className="h-3 w-3 mr-1" />
                        Not Verified
                      </>
                    )}
                  </span>
                </div>

                {/* Actions - 3 columns */}
                <div className="col-span-3 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    {registration.attended && (
                      <button className="p-2 text-gray-500 hover:text-[#a8a4c5] transition-colors" title="Download Certificate">
                        <Download className="h-4 w-4" />
                      </button>
                    )}
                    <Link
                      href={`/dashboard/student/events/${registration.event.id}`}
                      className="px-4 py-2 bg-[#a8a4c5] text-white rounded-lg text-sm hover:bg-[#8e8aab] transition-colors flex items-center"
                    >
                      <FileText className="h-4 w-4 mr-1" />
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
