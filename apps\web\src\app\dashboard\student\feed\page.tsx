'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ResponsiveGrid } from '@/components/ui/responsive-card';
import { PageHeader, PageContainer, EmptyState } from '@/components/ui/page-header';
import {
  Search,
  Speaker,
  CalendarDays,
  Pencil,
  ThumbsUp,
  ThumbsDown,
  Globe,
  FileText,
  Heart,
  MessageCircle,
  Share2,
  Clock,
  MapPin,
  Users
} from 'lucide-react';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  club: {
    id: string;
    name: string;
    description: string;
  } | null;
  createdAt?: string;
  likes?: number;
  dislikes?: number;
  userLiked?: boolean;
  userDisliked?: boolean;
  imageUrl?: string;
  attendeeCount?: number;
}

export default function StudentFeed() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [likeLoading, setLikeLoading] = useState<string | null>(null);
  const router = useRouter();
  const { user, token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login/student');
      return;
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    const fetchEvents = async () => {
      if (!isAuthenticated || !user) return;

      try {
        const response = await fetch('http://localhost:3001/api/events', {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'user-id': user.id
          }
        });
        
        if (!response.ok) throw new Error('Failed to fetch events');
        
        const data = await response.json();
        setEvents(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [isAuthenticated, user, token]);

  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLike = async (eventId: string) => {
    if (!isAuthenticated || !user || likeLoading) return;

    setLikeLoading(eventId);
    try {
      const event = events.find(e => e.id === eventId);
      if (!event) return;

      const response = await fetch(`http://localhost:3001/api/events/${eventId}/like`, {
        method: event.userLiked ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'user-id': user.id
        }
      });

      if (response.ok) {
        setEvents(prevEvents =>
          prevEvents.map(e => {
            if (e.id === eventId) {
              const newLikes = e.userLiked ? (e.likes || 0) - 1 : (e.likes || 0) + 1;
              const newDislikes = e.userDisliked ? (e.dislikes || 0) - 1 : e.dislikes || 0;
              return {
                ...e,
                likes: newLikes,
                dislikes: newDislikes,
                userLiked: !e.userLiked,
                userDisliked: false
              };
            }
            return e;
          })
        );
      }
    } catch (err) {
      console.error('Error liking event:', err);
    } finally {
      setLikeLoading(null);
    }
  };

  const handleDislike = async (eventId: string) => {
    if (!isAuthenticated || !user || likeLoading) return;

    setLikeLoading(eventId);
    try {
      const event = events.find(e => e.id === eventId);
      if (!event) return;

      const response = await fetch(`http://localhost:3001/api/events/${eventId}/dislike`, {
        method: event.userDisliked ? 'DELETE' : 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'user-id': user.id
        }
      });

      if (response.ok) {
        setEvents(prevEvents =>
          prevEvents.map(e => {
            if (e.id === eventId) {
              const newDislikes = e.userDisliked ? (e.dislikes || 0) - 1 : (e.dislikes || 0) + 1;
              const newLikes = e.userLiked ? (e.likes || 0) - 1 : e.likes || 0;
              return {
                ...e,
                likes: newLikes,
                dislikes: newDislikes,
                userLiked: false,
                userDisliked: !e.userDisliked
              };
            }
            return e;
          })
        );
      }
    } catch (err) {
      console.error('Error disliking event:', err);
    } finally {
      setLikeLoading(null);
    }
  };

  if (!isAuthenticated || !user) {
    return <div className="text-center text-gray-700">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <div className="h-10 bg-gray-200 rounded-lg w-48 mb-2 animate-pulse"></div>
              <div className="h-6 bg-gray-200 rounded-lg w-64 animate-pulse"></div>
            </div>
            <div className="h-12 bg-gray-200 rounded-xl w-full sm:w-80 animate-pulse"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="h-48 sm:h-56 bg-gray-200 animate-pulse"></div>
              <div className="p-6">
                <div className="h-6 bg-gray-200 rounded-lg w-3/4 mb-4 animate-pulse"></div>
                <div className="space-y-2 mb-4">
                  <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-6 animate-pulse"></div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="h-8 w-24 bg-gray-200 rounded-xl animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <PageContainer>
      <PageHeader
        title="Feed"
        description="Stay updated with previous and upcoming events"
        icon={<FileText />}
      >
        {/* Search Bar */}
        <div className="relative w-full sm:w-auto sm:min-w-[300px]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            type="text"
            placeholder="Search events..."
            className="pl-10 pr-4 py-3 border border-gray-300 rounded-xl w-full bg-white/80 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent placeholder-gray-400 shadow-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </PageHeader>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <p className="text-red-600 font-medium">Error: {error}</p>
        </div>
      )}

      {/* Events Grid */}
      <ResponsiveGrid cols={{ default: 1, lg: 2 }} gap="lg">
        {filteredEvents.length > 0 ? (
          filteredEvents.map((event) => (
            <div key={event.id} className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-lg hover:border-[#a8a4c5]/20 transition-all duration-300 group">
              {/* Event Image */}
              <div className="relative h-48 sm:h-56 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                <Image
                  src={event.imageUrl || "/placeholder-event.png"}
                  alt={event.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-gray-700">
                    {event.club?.name || 'GC Student Council'}
                  </span>
                </div>
                {event.attendeeCount && (
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-[#a8a4c5]/90 backdrop-blur-sm rounded-full text-xs font-medium text-white flex items-center">
                      <Users className="h-3 w-3 mr-1" />
                      {event.attendeeCount}
                    </span>
                  </div>
                )}
              </div>

              <div className="p-6">
                {/* Event Title and Meta */}
                <div className="mb-4">
                  <h2 className="text-xl font-bold text-gray-800 mb-2 line-clamp-2 group-hover:text-[#a8a4c5] transition-colors">
                    {event.title}
                  </h2>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center">
                      <CalendarDays className="h-4 w-4 mr-1" />
                      <span>{new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{new Date(`2000-01-01T${event.startTime}`).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="truncate">{event.location}</span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-700 mb-4 line-clamp-3 leading-relaxed">{event.description}</p>

                {/* Author */}
                <div className="flex items-center text-sm text-gray-500 mb-6">
                  <Pencil className="h-4 w-4 mr-2" />
                  <span className="font-medium">{event.createdBy.name}</span>
                  <span className="mx-2">•</span>
                  <span>{new Date(event.createdAt || event.date).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })}</span>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 sm:space-x-1">
                    {/* Like Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleLike(event.id)}
                      disabled={likeLoading === event.id}
                      className={`p-2 sm:p-2 h-auto min-h-[44px] sm:min-h-auto transition-all duration-200 ${
                        event.userLiked
                          ? 'text-red-500 bg-red-50 hover:bg-red-100'
                          : 'text-gray-500 hover:text-red-500 hover:bg-red-50'
                      }`}
                    >
                      <Heart className={`h-5 w-5 ${event.userLiked ? 'fill-current' : ''}`} />
                    </Button>
                    <span className="text-sm font-medium text-gray-600 min-w-[20px]">
                      {event.likes || 0}
                    </span>

                    {/* Dislike Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDislike(event.id)}
                      disabled={likeLoading === event.id}
                      className={`p-2 sm:p-2 h-auto min-h-[44px] sm:min-h-auto transition-all duration-200 ${
                        event.userDisliked
                          ? 'text-blue-500 bg-blue-50 hover:bg-blue-100'
                          : 'text-gray-500 hover:text-blue-500 hover:bg-blue-50'
                      }`}
                    >
                      <ThumbsDown className={`h-5 w-5 ${event.userDisliked ? 'fill-current' : ''}`} />
                    </Button>
                    <span className="text-sm font-medium text-gray-600 min-w-[20px]">
                      {event.dislikes || 0}
                    </span>

                    {/* Comment Button */}
                    <Button variant="ghost" size="sm" className="p-2 sm:p-2 h-auto min-h-[44px] sm:min-h-auto text-gray-500 hover:text-[#a8a4c5] hover:bg-[#a8a4c5]/10 transition-all duration-200">
                      <MessageCircle className="h-5 w-5" />
                    </Button>

                    {/* Share Button */}
                    <Button variant="ghost" size="sm" className="p-2 sm:p-2 h-auto min-h-[44px] sm:min-h-auto text-gray-500 hover:text-[#a8a4c5] hover:bg-[#a8a4c5]/10 transition-all duration-200">
                      <Share2 className="h-5 w-5" />
                    </Button>
                  </div>

                  <Link
                    href={`/dashboard/student/events/${event.id}`}
                    className="bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] text-white px-6 py-2 rounded-xl text-sm font-medium hover:shadow-lg hover:scale-105 transition-all duration-200"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full">
            <EmptyState
              icon={<FileText />}
              title="No events found"
              description="Try adjusting your search terms or check back later for new events."
            />
          </div>
        )}
      </ResponsiveGrid>
    </PageContainer>
  );
}
