'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  User as UserIcon,
  UploadCloud,
  Trash2,
  Camera,
  Save,
  Mail,
  GraduationCap,
  Calendar,
  Hash,
  Edit3
} from 'lucide-react';

interface Profile {
  id: string;
  name: string;
  email: string;
  studentId: string;
  course: string;
  year: number;
  section: string;
  bio?: string;
  avatar?: string;
  firstName?: string;
  lastName?: string;
}

export default function StudentProfile() {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Profile>>({});
  const [saving, setSaving] = useState(false);
  const [uploadingAvatar, setUploadingAvatar] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const router = useRouter();
  const { user, token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login/student');
      return;
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!isAuthenticated || !user) return;

      try {
        const response = await fetch('http://localhost:3001/api/profile', {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) throw new Error('Failed to fetch profile');
        
        const data = await response.json();
        const [firstName, ...lastNameParts] = (data.name || '').split(' ');
        const lastName = lastNameParts.join(' ');

        setProfile(data);
        setFormData({ 
          ...data, 
          firstName: firstName || '', 
          lastName: lastName || '' 
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [isAuthenticated, user, token]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !isAuthenticated || !user) return;

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file');
      return;
    }

    if (file.size > 15 * 1024 * 1024) { // 15MB limit
      setError('Image size must be less than 15MB');
      return;
    }

    setUploadingAvatar(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await fetch('http://localhost:3001/api/profile/avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload avatar');
      }

      const data = await response.json();
      setProfile(prev => prev ? { ...prev, avatar: data.avatarUrl } : null);
      setFormData(prev => ({ ...prev, avatar: data.avatarUrl }));
      setSuccessMessage('Profile picture updated successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload avatar');
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleDeleteAvatar = async () => {
    if (!isAuthenticated || !user) return;

    setUploadingAvatar(true);
    try {
      const response = await fetch('http://localhost:3001/api/profile/avatar', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setProfile(prev => prev ? { ...prev, avatar: undefined } : null);
        setFormData(prev => ({ ...prev, avatar: undefined }));
        setSuccessMessage('Profile picture removed successfully!');
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      setError('Failed to delete avatar');
    } finally {
      setUploadingAvatar(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated || !user) return;

    setSaving(true);
    setError(null);

    try {
      const dataToSubmit = {
        ...formData,
        name: `${formData.firstName || ''} ${formData.lastName || ''}`.trim()
      };

      if (!dataToSubmit.name || !dataToSubmit.email || !dataToSubmit.course || !dataToSubmit.year || !dataToSubmit.section) {
        setError('Please fill in all required fields');
        return;
      }

      const response = await fetch('http://localhost:3001/api/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(dataToSubmit)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }

      const data = await response.json();
      const [firstName, ...lastNameParts] = (data.name || '').split(' ');
      const lastName = lastNameParts.join(' ');

      setProfile(data);
      setFormData({
        ...data,
        firstName: firstName || '',
        lastName: lastName || ''
      });
      setSuccessMessage('Profile updated successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setSaving(false);
    }
  };

  if (!isAuthenticated || !user) {
    return <div className="text-center text-gray-700">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="h-10 bg-gray-200 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-64 animate-pulse"></div>
        </div>

        <div className="space-y-8">
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="h-16 bg-gray-200 animate-pulse"></div>
            <div className="p-6">
              <div className="flex flex-col sm:flex-row sm:items-center gap-6 mb-8 p-6 bg-gray-50 rounded-xl">
                <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-gray-200 animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-6 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
                  <div className="flex gap-3">
                    <div className="h-8 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div className="h-8 w-20 bg-gray-200 rounded-lg animate-pulse"></div>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                    <div className="h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="h-16 bg-gray-200 animate-pulse"></div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                    <div className="h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <div className="h-12 w-32 bg-gray-200 rounded-xl animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {/* Header Section */}
      <div className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 flex items-center mb-2">
          <div className="p-2 bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab] rounded-xl mr-3">
            <UserIcon className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
          </div>
          Profile
        </h1>
        <p className="text-gray-600 text-lg">Manage your profile information</p>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
          <p className="text-green-600 font-medium">{successMessage}</p>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
          <p className="text-red-600 font-medium">{error}</p>
        </div>
      )}
      
      {profile && (
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Card */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] px-6 py-4">
              <h2 className="text-lg font-bold text-white flex items-center">
                <UserIcon className="h-5 w-5 mr-2" />
                Personal Information
              </h2>
            </div>

            <div className="p-6">
              {/* Profile Picture Section */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-6 mb-8 p-6 bg-gray-50 rounded-xl">
                <div className="relative">
                  <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center overflow-hidden border-4 border-white shadow-lg">
                    {profile.avatar ? (
                      <Image src={profile.avatar} alt="Profile Picture" fill className="object-cover" />
                    ) : (
                      <UserIcon className="h-12 w-12 sm:h-16 sm:w-16 text-gray-500" />
                    )}
                  </div>
                  {uploadingAvatar && (
                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                      <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>

                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">Profile Picture</h3>
                  <p className="text-sm text-gray-500 mb-4">PNG, JPEG under 15MB. Recommended size: 400x400px</p>
                  <div className="flex flex-wrap gap-3">
                    <label className="relative">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                        disabled={uploadingAvatar}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                      />
                      <button
                        type="button"
                        disabled={uploadingAvatar}
                        className="px-4 py-2 text-sm bg-[#a8a4c5] text-white rounded-lg hover:bg-[#8e8aab] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Camera className="h-4 w-4 mr-2" />
                        {uploadingAvatar ? 'Uploading...' : 'Upload New'}
                      </button>
                    </label>
                    {profile.avatar && (
                      <button
                        type="button"
                        onClick={handleDeleteAvatar}
                        disabled={uploadingAvatar}
                        className="px-4 py-2 text-sm bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-semibold text-gray-700">
                    First Name *
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your first name"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-semibold text-gray-700">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                    placeholder="Enter your last name"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="studentId" className="flex items-center text-sm font-semibold text-gray-700">
                    <Hash className="h-4 w-4 mr-1" />
                    Student Number
                  </label>
                  <input
                    type="text"
                    id="studentId"
                    name="studentId"
                    value={formData.studentId || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm bg-gray-50 text-gray-500 cursor-not-allowed"
                    disabled
                    placeholder="Auto-assigned"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="email" className="flex items-center text-sm font-semibold text-gray-700">
                    <Mail className="h-4 w-4 mr-1" />
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm bg-gray-50 text-gray-500 cursor-not-allowed"
                    disabled
                    placeholder="Institutional email"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Education Information Card */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] px-6 py-4">
              <h2 className="text-lg font-bold text-white flex items-center">
                <GraduationCap className="h-5 w-5 mr-2" />
                Education Information
              </h2>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="college" className="block text-sm font-semibold text-gray-700">
                    College *
                  </label>
                  <input
                    type="text"
                    id="college"
                    name="course"
                    value={formData.course || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                    placeholder="e.g., College of Computer Studies"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="program" className="block text-sm font-semibold text-gray-700">
                    Program
                  </label>
                  <input
                    type="text"
                    id="program"
                    name="program"
                    value={profile.course || ''}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                    placeholder="e.g., Bachelor of Science in Computer Science"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="yearLevel" className="flex items-center text-sm font-semibold text-gray-700">
                    <Calendar className="h-4 w-4 mr-1" />
                    Year Level *
                  </label>
                  <select
                    id="yearLevel"
                    name="year"
                    value={formData.year || ''}
                    onChange={(e) => setFormData({ ...formData, year: parseInt(e.target.value) })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                  >
                    <option value="">Select year level</option>
                    <option value={1}>1st Year</option>
                    <option value={2}>2nd Year</option>
                    <option value={3}>3rd Year</option>
                    <option value={4}>4th Year</option>
                    <option value={5}>5th Year</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="block" className="block text-sm font-semibold text-gray-700">
                    Block/Section *
                  </label>
                  <input
                    type="text"
                    id="block"
                    name="section"
                    value={formData.section || ''}
                    onChange={(e) => setFormData({ ...formData, section: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#a8a4c5] focus:border-transparent transition-all duration-200"
                    placeholder="e.g., Block A, Section 1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="px-8 py-3 bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] text-white font-semibold rounded-xl hover:shadow-lg hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center"
            >
              {saving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}