'use client';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import {
  LayoutGrid, // For Dashboard header
  Award, // For Total Hours badge
  Star, // For Events Attended stars
  FileText, // For Total Certificates
  Clock, // For Upcoming Events
  TrendingUp,
  Calendar,
  Users,
  Target,
  ChevronRight,
  Download,
  Eye
} from 'lucide-react';

interface UserStats {
  userId: string;
  totalEvents: number;
  registeredEvents: number;
  attendedEvents: number;
  upcomingEvents: number;
  totalHours: number;
  favoriteClubs: string[];
  recentActivity: {
    eventId: string;
    eventTitle: string;
    date: string;
    status: 'registered' | 'attended';
    hoursEarned: number;
  }[];
}

export default function StudentStats() {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { user, token, isAuthenticated } = useAuth();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login/student');
      return;
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    const fetchStats = async () => {
      if (!isAuthenticated || !user) return;

      try {
        const response = await fetch(`http://localhost:3001/api/stats/user/${user.id}`, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            'X-User-ID': user.id,
            'X-User-Role': user.role
          }
        });
        
        if (!response.ok) throw new Error('Failed to fetch stats');
        
        const data = await response.json();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [isAuthenticated, user, token]);

  if (!isAuthenticated || !user) {
    return <div className="text-center text-gray-700">Loading...</div>;
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="h-10 bg-gray-200 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded-lg w-64 animate-pulse"></div>
        </div>

        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-200 p-6 rounded-2xl shadow-lg animate-pulse">
                <div className="h-32"></div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-2xl shadow-sm border border-gray-100">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>

          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="h-16 bg-gray-200 animate-pulse"></div>
            <div className="p-6">
              <div className="h-32 bg-gray-200 rounded-xl animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      {/* Header Section */}
      <div className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 flex items-center mb-2">
          <div className="p-2 bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab] rounded-xl mr-3">
            <LayoutGrid className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
          </div>
          Dashboard
        </h1>
        <p className="text-gray-600 text-lg">Track your volunteer hours and achievements</p>
      </div>

      {error && <p className="text-red-500 mb-4">Error: {error}</p>}
      
      {stats && (
        <div className="space-y-8">
          {/* Main Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {/* Total Hours Card */}
            <div className="bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab] p-6 rounded-2xl shadow-lg text-white relative overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Total Hours</h3>
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Clock className="h-5 w-5" />
                  </div>
                </div>
                <p className="text-4xl sm:text-5xl font-bold mb-2">{stats.totalHours}</p>
                <p className="text-white/80 text-sm mb-4">Hours completed</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm">
                    <Award className="h-4 w-4 mr-1" />
                    <span className="font-medium">
                      {stats.totalHours >= 100 ? 'EXPERT VOLUNTEER' :
                       stats.totalHours >= 50 ? 'ACTIVE VOLUNTEER' :
                       'ROOKIE VOLUNTEER'}
                    </span>
                  </div>
                  <Link href="#" className="flex items-center text-white/80 hover:text-white transition-colors group">
                    <span className="text-sm">View Details</span>
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </div>

            {/* Events Attended Card */}
            <div className="bg-gradient-to-br from-[#7a8c9e] to-[#6a7c8f] p-6 rounded-2xl shadow-lg text-white relative overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Events Attended</h3>
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Calendar className="h-5 w-5" />
                  </div>
                </div>
                <p className="text-4xl sm:text-5xl font-bold mb-2">{stats.attendedEvents}</p>
                <p className="text-white/80 text-sm mb-4">Events completed</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 mr-1 ${i < Math.min(stats.attendedEvents, 5) ? 'text-yellow-400' : 'text-white/30'}`}
                        fill="currentColor"
                      />
                    ))}
                  </div>
                  <Link href="/dashboard/student/events" className="flex items-center text-white/80 hover:text-white transition-colors group">
                    <span className="text-sm">View Events</span>
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </div>

            {/* Total Certificates Card */}
            <div className="bg-gradient-to-br from-[#8e8aab] to-[#7e7a9b] p-6 rounded-2xl shadow-lg text-white relative overflow-hidden group hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Certificates</h3>
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Award className="h-5 w-5" />
                  </div>
                </div>
                <p className="text-4xl sm:text-5xl font-bold mb-2">{stats.attendedEvents}</p>
                <p className="text-white/80 text-sm mb-4">Certificates earned</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-sm">
                    <FileText className="h-4 w-4 mr-1" />
                    <span className="font-medium">Available for download</span>
                  </div>
                  <button className="flex items-center text-white/80 hover:text-white transition-colors group">
                    <span className="text-sm">View All</span>
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Secondary Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
            {/* Registered Events */}
            <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                <div className="p-2 bg-blue-100 rounded-lg mb-2 sm:mb-0 self-start">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                </div>
                <span className="text-xl sm:text-2xl font-bold text-gray-800">{stats.registeredEvents}</span>
              </div>
              <h3 className="text-xs sm:text-sm font-semibold text-gray-600 mb-1">Registered Events</h3>
              <p className="text-xs text-gray-500 hidden sm:block">Events you&apos;ve signed up for</p>
            </div>

            {/* Upcoming Events */}
            <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                <div className="p-2 bg-green-100 rounded-lg mb-2 sm:mb-0 self-start">
                  <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                </div>
                <span className="text-xl sm:text-2xl font-bold text-gray-800">{stats.upcomingEvents}</span>
              </div>
              <h3 className="text-xs sm:text-sm font-semibold text-gray-600 mb-1">Upcoming Events</h3>
              <p className="text-xs text-gray-500 hidden sm:block">Events happening soon</p>
            </div>

            {/* Completion Rate */}
            <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                <div className="p-2 bg-purple-100 rounded-lg mb-2 sm:mb-0 self-start">
                  <Target className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
                </div>
                <span className="text-xl sm:text-2xl font-bold text-gray-800">
                  {stats.registeredEvents > 0 ? Math.round((stats.attendedEvents / stats.registeredEvents) * 100) : 0}%
                </span>
              </div>
              <h3 className="text-xs sm:text-sm font-semibold text-gray-600 mb-1">Completion Rate</h3>
              <p className="text-xs text-gray-500 hidden sm:block">Events attended vs registered</p>
            </div>

            {/* Average Hours */}
            <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4">
                <div className="p-2 bg-orange-100 rounded-lg mb-2 sm:mb-0 self-start">
                  <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-orange-600" />
                </div>
                <span className="text-xl sm:text-2xl font-bold text-gray-800">
                  {stats.attendedEvents > 0 ? Math.round(stats.totalHours / stats.attendedEvents) : 0}
                </span>
              </div>
              <h3 className="text-xs sm:text-sm font-semibold text-gray-600 mb-1">Avg Hours/Event</h3>
              <p className="text-xs text-gray-500 hidden sm:block">Average hours per event</p>
            </div>
          </div>

          {/* Upcoming Events Section */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] px-6 py-4">
              <h2 className="text-lg font-bold text-white flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Upcoming Events
              </h2>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                {/* Sample upcoming event - this would be dynamic */}
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-[#a8a4c5] rounded-lg flex items-center justify-center">
                      <Calendar className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">CCS Week</h3>
                      <p className="text-sm text-gray-500">In 2 days • Room 305</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-500 hover:text-[#a8a4c5] transition-colors">
                      <Eye className="h-4 w-4" />
                    </button>
                    <Link
                      href="/dashboard/student/events"
                      className="px-4 py-2 bg-[#a8a4c5] text-white rounded-lg text-sm hover:bg-[#8e8aab] transition-colors"
                    >
                      View Details
                    </Link>
                  </div>
                </div>

                <div className="text-center py-4">
                  <Link
                    href="/dashboard/student/events"
                    className="text-[#a8a4c5] hover:text-[#8e8aab] font-medium text-sm flex items-center justify-center"
                  >
                    View All Events
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}