'use client';

import React, { useState } from 'react';
import { Alert<PERSON>riangle, Trash2, Check<PERSON>ircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  loading?: boolean;
}

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'warning',
  loading = false
}: ConfirmationDialogProps) {
  if (!isOpen) return null;

  const icons = {
    danger: Trash2,
    warning: AlertTriangle,
    info: CheckCircle,
  };

  const iconStyles = {
    danger: 'bg-red-100 text-red-600',
    warning: 'bg-yellow-100 text-yellow-600',
    info: 'bg-blue-100 text-blue-600',
  };

  const buttonStyles = {
    danger: 'bg-red-600 hover:bg-red-700 text-white',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white',
    info: 'bg-blue-600 hover:bg-blue-700 text-white',
  };

  const Icon = icons[type];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center pb-4">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${iconStyles[type]}`}>
            <Icon className="w-8 h-8" />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-gray-600 text-center">
            {message}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="flex-1"
            >
              {cancelText}
            </Button>
            <Button
              onClick={onConfirm}
              disabled={loading}
              className={`flex-1 ${buttonStyles[type]}`}
            >
              {loading ? 'Processing...' : confirmText}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Hook for easier confirmation dialog usage
export function useConfirmationDialog() {
  const [dialog, setDialog] = useState<{
    isOpen: boolean;
    title?: string;
    message?: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'danger' | 'warning' | 'info';
    onConfirm?: () => void;
  }>({
    isOpen: false,
  });

  const [loading, setLoading] = useState(false);

  const showDialog = (options: {
    title?: string;
    message?: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'danger' | 'warning' | 'info';
    onConfirm: () => void | Promise<void>;
  }) => {
    setDialog({
      isOpen: true,
      ...options,
      onConfirm: async () => {
        setLoading(true);
        try {
          await options.onConfirm();
          setDialog({ isOpen: false });
        } catch (error) {
          console.error('Confirmation action failed:', error);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const hideDialog = () => {
    if (!loading) {
      setDialog({ isOpen: false });
    }
  };

  const confirmDelete = (onConfirm: () => void | Promise<void>, itemName?: string) => {
    showDialog({
      title: 'Delete Item',
      message: `Are you sure you want to delete ${itemName || 'this item'}? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      type: 'danger',
      onConfirm,
    });
  };

  const confirmAction = (
    onConfirm: () => void | Promise<void>,
    title?: string,
    message?: string
  ) => {
    showDialog({
      title: title || 'Confirm Action',
      message: message || 'Are you sure you want to proceed?',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      type: 'warning',
      onConfirm,
    });
  };

  const DialogComponent = () => (
    <ConfirmationDialog
      isOpen={dialog.isOpen}
      onClose={hideDialog}
      onConfirm={dialog.onConfirm || (() => {})}
      title={dialog.title}
      message={dialog.message}
      confirmText={dialog.confirmText}
      cancelText={dialog.cancelText}
      type={dialog.type}
      loading={loading}
    />
  );

  return {
    showDialog,
    hideDialog,
    confirmDelete,
    confirmAction,
    DialogComponent,
    loading,
  };
}

// Quick confirmation function for simple use cases
export function confirm(
  message: string,
  title?: string,
  type: 'danger' | 'warning' | 'info' = 'warning'
): Promise<boolean> {
  return new Promise((resolve) => {
    const dialog = document.createElement('div');
    dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50';
    
    const handleConfirm = () => {
      document.body.removeChild(dialog);
      resolve(true);
    };
    
    const handleCancel = () => {
      document.body.removeChild(dialog);
      resolve(false);
    };

    const icons = {
      danger: '🗑️',
      warning: '⚠️',
      info: 'ℹ️',
    };

    const colors = {
      danger: 'bg-red-600 hover:bg-red-700',
      warning: 'bg-yellow-600 hover:bg-yellow-700',
      info: 'bg-blue-600 hover:bg-blue-700',
    };

    dialog.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="text-center mb-6">
          <div class="text-4xl mb-4">${icons[type]}</div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">${title || 'Confirm Action'}</h3>
          <p class="text-gray-600">${message}</p>
        </div>
        <div class="flex gap-3">
          <button id="cancel-btn" class="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
            Cancel
          </button>
          <button id="confirm-btn" class="flex-1 px-4 py-2 ${colors[type]} text-white rounded-md transition-colors">
            Confirm
          </button>
        </div>
      </div>
    `;

    dialog.querySelector('#confirm-btn')?.addEventListener('click', handleConfirm);
    dialog.querySelector('#cancel-btn')?.addEventListener('click', handleCancel);
    dialog.addEventListener('click', (e) => {
      if (e.target === dialog) handleCancel();
    });

    document.body.appendChild(dialog);
  });
}
