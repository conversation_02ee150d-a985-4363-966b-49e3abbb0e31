import * as React from "react"
import { cn } from "@/lib/utils"
import { getGradientClasses } from "@/lib/theme"

interface PageHeaderProps {
  title: string
  description?: string
  icon?: React.ReactNode
  children?: React.ReactNode
  className?: string
}

export function PageHeader({ 
  title, 
  description, 
  icon, 
  children, 
  className 
}: PageHeaderProps) {
  return (
    <div className={cn("mb-8", className)}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 flex items-center mb-2">
            {icon && (
              <div className={cn(
                "p-2 rounded-xl mr-3",
                getGradientClasses('primary')
              )}>
                <div className="h-6 w-6 sm:h-8 sm:w-8 text-white">
                  {icon}
                </div>
              </div>
            )}
            {title}
          </h1>
          {description && (
            <p className="text-gray-600 text-lg">{description}</p>
          )}
        </div>
        
        {children && (
          <div className="flex-shrink-0">
            {children}
          </div>
        )}
      </div>
    </div>
  )
}

interface PageContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '7xl'
}

export function PageContainer({ 
  children, 
  className,
  maxWidth = '7xl'
}: PageContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md', 
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    '7xl': 'max-w-7xl'
  }

  return (
    <div className={cn(
      maxWidthClasses[maxWidth],
      "mx-auto py-6 px-4 sm:px-6 lg:px-8",
      className
    )}>
      {children}
    </div>
  )
}

interface StatusBadgeProps {
  status: string
  variant?: 'default' | 'outline'
  size?: 'sm' | 'md'
  icon?: React.ReactNode
  className?: string
}

export function StatusBadge({ 
  status, 
  variant = 'default',
  size = 'sm',
  icon,
  className 
}: StatusBadgeProps) {
  const getStatusStyles = (status: string) => {
    switch (status.toLowerCase()) {
      case 'registered':
      case 'verified':
      case 'completed':
        return variant === 'outline' 
          ? 'border-green-200 text-green-700 bg-transparent'
          : 'bg-green-100 text-green-700'
      case 'pending':
        return variant === 'outline'
          ? 'border-yellow-200 text-yellow-700 bg-transparent'
          : 'bg-yellow-100 text-yellow-700'
      case 'cancelled':
      case 'failed':
        return variant === 'outline'
          ? 'border-red-200 text-red-700 bg-transparent'
          : 'bg-red-100 text-red-700'
      case 'not verified':
        return variant === 'outline'
          ? 'border-gray-200 text-gray-700 bg-transparent'
          : 'bg-gray-100 text-gray-700'
      default:
        return variant === 'outline'
          ? 'border-gray-200 text-gray-700 bg-transparent'
          : 'bg-gray-100 text-gray-700'
    }
  }

  const sizeClasses = {
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-2 text-sm'
  }

  return (
    <span className={cn(
      "inline-flex items-center rounded-full font-medium",
      variant === 'outline' && "border",
      sizeClasses[size],
      getStatusStyles(status),
      className
    )}>
      {icon && <span className="mr-1">{icon}</span>}
      {status}
    </span>
  )
}

interface LoadingSkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular'
  width?: string | number
  height?: string | number
}

export function LoadingSkeleton({ 
  className,
  variant = 'rectangular',
  width,
  height
}: LoadingSkeletonProps) {
  const variantClasses = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg'
  }

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  }

  return (
    <div 
      className={cn(
        "bg-gray-200 animate-pulse",
        variantClasses[variant],
        className
      )}
      style={style}
    />
  )
}

interface EmptyStateProps {
  icon?: React.ReactNode
  title: string
  description?: string
  action?: React.ReactNode
  className?: string
}

export function EmptyState({
  icon,
  title,
  description,
  action,
  className
}: EmptyStateProps) {
  return (
    <div className={cn("text-center py-12", className)}>
      <div className="max-w-md mx-auto">
        {icon && (
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <div className="h-8 w-8 text-gray-400">
              {icon}
            </div>
          </div>
        )}
        <h3 className="text-lg font-medium text-gray-800 mb-2">{title}</h3>
        {description && (
          <p className="text-gray-500 mb-6">{description}</p>
        )}
        {action && action}
      </div>
    </div>
  )
}
