import * as React from "react"
import { cn } from "@/lib/utils"

interface ResponsiveCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'gradient' | 'bordered'
  padding?: 'sm' | 'md' | 'lg'
}

const ResponsiveCard = React.forwardRef<HTMLDivElement, ResponsiveCardProps>(
  ({ className, children, variant = 'default', padding = 'md', ...props }, ref) => {
    const baseClasses = "rounded-2xl shadow-sm transition-all duration-200 hover:shadow-md"
    
    const variantClasses = {
      default: "bg-white border border-gray-100",
      gradient: "bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab] text-white",
      bordered: "bg-white border-2 border-[#a8a4c5]/20"
    }
    
    const paddingClasses = {
      sm: "p-4",
      md: "p-6",
      lg: "p-8"
    }

    return (
      <div
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          paddingClasses[padding],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveCard.displayName = "ResponsiveCard"

interface ResponsiveCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  icon?: React.ReactNode
  gradient?: boolean
}

const ResponsiveCardHeader = React.forwardRef<HTMLDivElement, ResponsiveCardHeaderProps>(
  ({ className, children, icon, gradient = false, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center mb-4",
          gradient && "bg-gradient-to-r from-[#a8a4c5] to-[#8e8aab] -m-6 mb-6 px-6 py-4 text-white",
          className
        )}
        {...props}
      >
        {icon && (
          <div className={cn(
            "p-2 rounded-lg mr-3",
            gradient ? "bg-white/20" : "bg-[#a8a4c5]/10"
          )}>
            {icon}
          </div>
        )}
        <div className="flex-1">
          {children}
        </div>
      </div>
    )
  }
)
ResponsiveCardHeader.displayName = "ResponsiveCardHeader"

interface ResponsiveCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const ResponsiveCardContent = React.forwardRef<HTMLDivElement, ResponsiveCardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("space-y-4", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveCardContent.displayName = "ResponsiveCardContent"

interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: 'sm' | 'md' | 'lg'
}

const ResponsiveGrid = React.forwardRef<HTMLDivElement, ResponsiveGridProps>(
  ({ className, children, cols = { default: 1, md: 2, lg: 3 }, gap = 'md', ...props }, ref) => {
    const gridCols = {
      1: "grid-cols-1",
      2: "grid-cols-2", 
      3: "grid-cols-3",
      4: "grid-cols-4",
      5: "grid-cols-5",
      6: "grid-cols-6"
    }
    
    const gapClasses = {
      sm: "gap-4",
      md: "gap-6",
      lg: "gap-8"
    }
    
    const responsiveClasses = [
      cols.default && gridCols[cols.default],
      cols.sm && `sm:${gridCols[cols.sm]}`,
      cols.md && `md:${gridCols[cols.md]}`,
      cols.lg && `lg:${gridCols[cols.lg]}`,
      cols.xl && `xl:${gridCols[cols.xl]}`
    ].filter(Boolean).join(' ')

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          responsiveClasses,
          gapClasses[gap],
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ResponsiveGrid.displayName = "ResponsiveGrid"

export { 
  ResponsiveCard, 
  ResponsiveCardHeader, 
  ResponsiveCardContent, 
  ResponsiveGrid 
}
