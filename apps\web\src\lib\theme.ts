// Theme configuration for GC Hub Student Dashboard
export const theme = {
  colors: {
    // Primary brand colors
    primary: {
      50: '#f8f7fc',
      100: '#f0eef8',
      200: '#e3dff2',
      300: '#d1c9e8',
      400: '#b8a9db',
      500: '#a8a4c5', // Main accent color
      600: '#8e8aab',
      700: '#7e7a9b',
      800: '#6a6682',
      900: '#565369',
    },
    
    // Secondary colors
    secondary: {
      50: '#f8fafb',
      100: '#f1f5f7',
      200: '#e4ebef',
      300: '#d1dce3',
      400: '#b8c7d1',
      500: '#7a8c9e', // Table headers
      600: '#6a7c8f',
      700: '#5a6c7f',
      800: '#4a5c6f',
      900: '#3a4c5f',
    },
    
    // Sidebar
    sidebar: '#2c3e50',
    
    // Background
    background: '#faf7ef',
    
    // Status colors
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      500: '#22c55e',
      600: '#16a34a',
    },
    
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
    },
    
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
    },
    
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      500: '#3b82f6',
      600: '#2563eb',
    },
  },
  
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    '2xl': '3rem',   // 48px
    '3xl': '4rem',   // 64px
  },
  
  borderRadius: {
    sm: '0.375rem',  // 6px
    md: '0.5rem',    // 8px
    lg: '0.75rem',   // 12px
    xl: '1rem',      // 16px
    '2xl': '1.5rem', // 24px
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'monospace'],
    },
    
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
    },
    
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
    },
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  animation: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },
    
    easing: {
      ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
} as const

// Utility functions for consistent styling
export const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'registered':
    case 'verified':
    case 'completed':
    case 'success':
      return {
        bg: 'bg-green-100',
        text: 'text-green-700',
        border: 'border-green-200',
      }
    case 'pending':
    case 'warning':
      return {
        bg: 'bg-yellow-100',
        text: 'text-yellow-700',
        border: 'border-yellow-200',
      }
    case 'cancelled':
    case 'error':
    case 'failed':
      return {
        bg: 'bg-red-100',
        text: 'text-red-700',
        border: 'border-red-200',
      }
    case 'info':
    case 'not verified':
      return {
        bg: 'bg-blue-100',
        text: 'text-blue-700',
        border: 'border-blue-200',
      }
    default:
      return {
        bg: 'bg-gray-100',
        text: 'text-gray-700',
        border: 'border-gray-200',
      }
  }
}

export const getGradientClasses = (variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' = 'primary') => {
  switch (variant) {
    case 'primary':
      return 'bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab]'
    case 'secondary':
      return 'bg-gradient-to-br from-[#7a8c9e] to-[#6a7c8f]'
    case 'success':
      return 'bg-gradient-to-br from-green-500 to-green-600'
    case 'warning':
      return 'bg-gradient-to-br from-yellow-500 to-yellow-600'
    case 'error':
      return 'bg-gradient-to-br from-red-500 to-red-600'
    default:
      return 'bg-gradient-to-br from-[#a8a4c5] to-[#8e8aab]'
  }
}

export const getHoverClasses = (variant: 'primary' | 'secondary' | 'ghost' = 'primary') => {
  switch (variant) {
    case 'primary':
      return 'hover:shadow-lg hover:scale-105 transition-all duration-200'
    case 'secondary':
      return 'hover:shadow-md hover:bg-gray-50 transition-all duration-200'
    case 'ghost':
      return 'hover:bg-gray-100 transition-colors duration-200'
    default:
      return 'hover:shadow-lg hover:scale-105 transition-all duration-200'
  }
}
