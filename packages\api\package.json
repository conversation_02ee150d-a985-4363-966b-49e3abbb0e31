{"name": "api", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "tsx watch server.ts", "build": "tsc", "start": "node dist/server.js", "setup": "node create-schema-link.js", "prisma:generate": "node run-prisma.js generate", "prisma:migrate": "node run-prisma.js migrate dev", "prisma:studio": "node run-prisma.js studio", "db:init": "tsx scripts/init-db.ts", "db:test-users": "tsx scripts/insert-test-users.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.12.0", "@types/bcrypt": "^5.0.2", "bcrypt": "^6.0.0", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22.15.30", "prisma": "^6.9.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3"}}