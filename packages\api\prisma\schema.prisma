generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  student
  club
  organizer
  admin
}

enum ReportType {
  event_summary
  attendance
  hours
}

model User {
  id           String   @id @default(uuid())
  email        String   @unique
  name         String
  passwordHash String
  role         Role     @default(student)
  studentId    String?  @unique @map("student_id")
  course       String?
  year         Int?
  section      String?
  bio          String?
  avatar       String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  createdEvents        Event[]             @relation("UserCreatedEvents")
  registrations        EventRegistration[]
  club                 Club?               @relation("ClubLeader")
  reports              Report[]
  notifications        Notification[]
  createdAnnouncements Announcement[]      @relation("UserCreatedAnnouncements")
  stat                 Stat?

  @@map("users")
}

model Club {
  id           String         @id @default(uuid())
  name         String         @unique
  description  String
  leaderId     String         @unique
  leader       User           @relation("ClubLeader", fields: [leaderId], references: [id])
  events       Event[]
  createdAt    DateTime       @default(now())
  Announcement Announcement[]
}

model Event {
  id                  String              @id @default(uuid())
  title               String
  description         String
  date                DateTime
  startTime           String
  endTime             String
  location            String
  approved            Boolean             @default(false)
  createdById         String
  createdBy           User                @relation("UserCreatedEvents", fields: [createdById], references: [id])
  clubId              String?
  club                Club?               @relation(fields: [clubId], references: [id])
  registrations       EventRegistration[]
  createdAt           DateTime            @default(now())
  eventImageUrl       String?             @map("event_image_url")
  contactEmail        String?
  certificateProvided Boolean             @default(false)
  volunteerHours      Float?              @map("volunteer_hours")
}

model EventRegistration {
  id           String   @id @default(uuid())
  eventId      String
  userId       String
  event        Event    @relation(fields: [eventId], references: [id])
  user         User     @relation(fields: [userId], references: [id])
  registeredAt DateTime @default(now())
  attended     Boolean  @default(false)
  hoursEarned  Float?
}

model Report {
  id          String   @id @default(uuid())
  userId      String
  generatedBy User     @relation(fields: [userId], references: [id])
  type        String
  filters     Json
  fileUrl     String
  createdAt   DateTime @default(now())
}

model Stat {
  id          String   @id @default(uuid())
  userId      String   @unique
  user        User     @relation(fields: [userId], references: [id])
  totalEvents Int      @default(0)
  totalHours  Float    @default(0)
  updatedAt   DateTime @updatedAt
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  title     String
  message   String
  seen      Boolean  @default(false)
  createdAt DateTime @default(now())
}

model Announcement {
  id                   String   @id @default(uuid())
  title                String
  description          String
  datePosted           DateTime @default(now())
  announcementImageUrl String?  @map("announcement_image_url")
  clubId               String
  club                 Club     @relation(fields: [clubId], references: [id])
  createdById          String
  createdBy            User     @relation("UserCreatedAnnouncements", fields: [createdById], references: [id])
  createdAt            DateTime @default(now())

  @@map("announcements")
}
